 Ok用的比较多的一个就是语音性质检一个工单质检，质检再加上一个RPARPA的话，就是这个现在目前在执行的就是盘价。像我们现在有八台电脑，每个台是有两台电脑的，一种就是其中一个是机器人的，一个是自己办公的。然后这种呢一般情况下就是我们要跑的时候，就直接登录这个牌账，登录自己的这个系统之后，然后按照预先的设定的指令，他的机器人会帮你把一些些合呀，就是什么分担呀都会做了。我们这里说是一下二零二四年就是到二零二五年那几年创的一些之前老些公司。我们现在像我们这个团队涉及到的啊，就是有这个是最新做的，就是机器人对于那个退费的一个前置的一个机合。然后我们刚刚不是也看到有个电子审批批工单，然后发起之后有唐影的号进行退费的吗？那个唐椅的工号就是机器人的工号。那个工号呢，反正是发了电子审批之后，由机器人就是电。嗯，当领导通过了之后，由机器人直接进请催费操作。这个就是天天都在做，天天都在运行的月。然后退费了之后呢，直接就根据那个就是他的一个处理的情况，把那个费用退到客户的具体的装修方面，然后还有一些无质量级风险日报。这个现在目前的话呢嗯我们有在做，但是呢可能有一些没有太全。然后这个广通投诉的一个业务办理以及权益赠送，这个就是对于业务办理记录以及权益赠送来进行，根据指定条件进行审核的。然后这个指定条件审核之后呢，我们一般都会根据机器人输出的结果来进行人工复审。这个流量超套类的呢，这个不是不跟我们团队没有关系，但是跟专区整个是有关系的。这个就是通过对于流量的超超类的工单，由机器人前期去查询它的使用情况，账单的收费情况、退费情况。然后提供这个处理意见的投处理员，就是减少了同事处理员再去人工查的，直接根据机器人查的结果，回电话给客户就好。然后还有一个主动分担的，主动分担的呢，它是呃会发生变迁机器人。像有些他那个分担的原则，它会它会变化。像目前的话，他可能就是一个员工一套搬下来。最开始切进去系统，他可能会分五十张单，但是有些人他可能做的快，做的快，五张单很快就完了。他就会发起主动分单给机器人那个对应的编器人的工号。机器人就会根据这个指令，把对应的工单分到那个指定人的工号里面去。这个就就以这以前这些都是人工在做的，这些现在的变人机器人在做。然后还有这个工单预警及催单。这个也是以前是做那个超时的一个地方，也是有人工做的。现在都有机器人，它的实时受据量、工单量以及今天超时的工单量，通过发送邮件或者移动办公来进行申报。啊，有一些邮件的话，通过发发发送给对应的那个责任的一个他们来进行对办督办处理。还有一个服务预警的。我们这里现在这个我们这里还是考虑这是服务预警的，就是对于服务预警推诿的。我们先用自动质检，就是先用那个质呃那个语音加我工单的这种啊一些录音文本啊，接触记录啊，客户记录啊，办理界面。这些已经预算好一些。关键字那个模型命中了数据之后，由机器人跑跑那个数据，呃，根据我们前期授权的指令，然后来进行这轮分析判断，看有没有不合格的数据。然后再把这个所有集合的数据呢，对于在线的我们就会再进行两个复审。然后前期呢还会设置一个没有解决的就需要补救的。我们由机器人发起了阿里，但是因为那个那里面有时间性，所以暂时没有执行。最后这一环节去通过这些事，还有一个大米类的，大米类的呢就是也是根据呃跟那个当他的机器人差不多，他也是提供处理的一个意见，查证的情况来减少人工的一个处理时长。他们都没有。二零二四年，我们现在比较事情要成熟一点，举起来了，我们一定要主动，然他会看点别的。二零二五年的话，接下来的话，现在是是新的话是四百，这个蓝是位于一五八六的。好，也是根据那个抄取的键呀，都需要核查的，也进行进行行进一步处理的。这种也是由机器人来进行呃，说明在预处理的IPA的一个机器人，同时也是为了完成这个查证的时长。还有一个副卡优惠添加，副卡优惠添加。就是目前有很多客户，他一些副卡的那个减免的优惠啊都已经到期了。他就会再进行通过幺零零八六或者其他的渠道来进行投诉。再有我们投诉处理员，去手动给他添加这个优惠。但是现在呢就直接有这种语音，拨完拉铃之后呢，再由机器人来进行把这个符合条件的外呼，就是按照那个排序，他会每天签系统来进行跟客户打电话回复条件的，当场就给他办下发短信，客户二次采用之类的通符合条件的，或者是说啊就是有相关的一个拨报的一个话术播报话术就在这个里里边，他们会有前期都设置好进来，要要学的更及时。这个录音播放管理就会有设置好前期的付款优惠添加，还是这种异常处理的还已播报。这种已经设置好的录音内容。然后对于异常处理的，就可能直接通过那个机器人工号派发对应的模式工单，然后就到我诉诉这边来了。这个是呃上线，就是利用的比较好的，一天至少都有两到三台。然后多的话有时候要五台资信很好分一点多。然后这个钻金专席分单呢，是因为钻石卡呃，还有白金卡这些的这些客户的工单，他的处理时效性更短。所以这个要求为优先分担，这个也是由机器人直接分担的。就是通过对应的指令，把这个工单分给专门的专席处理人员也会处理这个工单。嗯，这个呢可能更高大上一点，就比前期的那个基础上，在这个前期的基础上呢，它运用大模型，还有袜呼，对于流量超耗的供单进行分解，服窗外护推送，智能袜呼就会跟那个付卡优惠机器人有一点点相同了。但是它可能更更那个点的是有利用大模型的一些数据来进行分解跟数据。这个就是机器人的一些真人手感。还有我们现在在做的有利用那个正品的正体，就是那个领运平台。我们在进行做那个一个是高频的一个智能体，还有做声报的一个智能体，还有做一个不满意的智能。体现在目前上线的可能就是这两个没有上线。这个重毛素的一个智能体呢，就是它会归纳归纳总结客户前期的投诉的一个处理情况，处理的方案谁处理的，我看一下有没有，就是这个等一下，我找他问一下，因为这个事情是就是已经做，然后这个就是一个归纳总结性的。然后它一个流量称号的一个完整性和补充信息的。这个是通过工单的编号以及接触的那个文本信息，查清楚客户是哪个月份的金额是多少，诉求是要求是多少。然后呢，针对模型把那个相关的一些要素补全，这个还是比较突人的。但是这个呢是我们配合在做的，不是，是楼上的一个一个专门做的这种需求的。就是因为我天天还是等我一下，我要找他要一张扇子，我要去去看一下这个工作，你有什么不好意思，通空奔布也是一个自己人，一定要在在搞这个事情吗？中文，我问一下工作，有就是没有啊，稍等一下，他看一下几个人，他有十年级七七年级，有这个吗？就是这几个人不认识他，其实你后面我看了一下，那个没有十以下的，二点级的还是蛮多的。没有这个，而且你是说下的还是蛮丰的，不人有等我一下啊，我找人换另外一家，有这样子的。嗯，就是哎我要带这种手工梳起来简单，其实做起来很好。嗯，开发还是要一定时间的对，这个就是这样。这个就是一个存话，维素，就是我们已经做款了的一个智能体的一个东西。所以这个传统维素非常好做，整体它会有它会有系统，不直接用AI生成。它会把前期的一些情况来先归纳焦点问题。客户诉求前期是理么？然后内胆哪上还款是多少，怎么样呀？就会有一个具体这个然后出台这些信息放在这个，然后面栏在哪里，你就会知道，你就不需要再去点击这个历史工单，再去查一下其实怎么处理的怎么样的。投色交点是什么，我就会直接有这个相当于就是一个总结功作。对，是的，就是你缩短了。你看前面的张工单的一个情况，你就看出了，这一目了然的。但是这个呢就是刚刚上线没多久了，好幽默。嗯，因为我没有，因为我没有没有负责这一块，所以这个不太好给予评价，但是现在已经上线了。好，然后这个就是我们刚刚所说到的一个个RPA,然后所说到的一个智能体。智能体就是我们今年可能比较要搞多一点的这个智能手段的，就是更更高大上一点，可能后续会有其他的，甚至什么APD.好，我们就就讲完这这一些了。然后我们再讲一下一百个一上角的呃工程智能质检和语言之能。就把近期针对抽检发现的一些新的问题，看有没有新的问光单之人之剪刀相对比较简单。语文之人之剪刀会不咋地的话，可以再讲一遍短信啊。然后就是最近这段时间近一个月会我们抽检发生了一个行动形容你，并且是及这个资这个这个资料已经很早很早以前的了。二零二一年的这个质检呢，语音智能质检在统一质检平台里面的模型配置清。这个所有的模型都会有各个部门以及本部进行设设置的。呃，现在目前看有一千三百多个了，我们随便看一个吧。每个月的时候都是很嗯里面有一些那种一红水工作室用后啊，我们看一个粉肤的这样的一个模型，导致里无法留览，但是并没有生法附件。你我这个我是放在在面面了吗？你比方说这个现在是我们本部自己建的一个商业纳税风险统建的模型，我们就可以看到它的模型名称，然后模型分类应用方向都后有关键字，关键字这个这个这个就这么多了，都是你打扰一下吗？啊，你说税款一该算，因为它也有三点零六五四零零，不知道怎么算。你这三点零的肯定是三点零的作业，对你作业下没关系的，完事了。你你里里联资资源肯定不能一起算的。抖音一下，我听了数据都在里面那个标准单一样的啊。第一个一个是啊，对对，不在吗？拿一下，因为我那没关吗？嗯，你你看一下后面那个详表，对，满意度看到没？四点零的，三点零的，就是这个四点零的就一张。对，那我这个离职伞不好搞呀，要不我就你你这个相当于你这相当于就是有三个客户不满意啊。对，但是你吃衣服你不能叫我不满意吧。那这两个客户买一点，我怎么算，你这总总共有多少张啊？嗯，一百零姐，一百零三吧，我觉得你不要在那后面就可以了，你这个就不写写了。对你看我的这个重头速率和申诉率是对的，不这么送吗？手机号码你我的计算器是什？是不是有那个吗？呃，五十三个除以九十七个，五十四点六四对的十八个除以九十七个。是啊，没错啊，那我就这个反，那我就不算了了。对啊，你你满意度不好算，为什么？你就相当于三个不满意，但是你有九十，你九十七个号码，你三个不满意，其他的等于说都没有回，是不是？你觉得要不要删了吧，没有删，你就放到这里，你就妈个脸没有嗯去把我的那关了，你关了。这因为这个u态里面应个是没关系的，而且我的搞不得，我们就以这个本步统计的模型看一下这个语音模型升级投诉风险的升级投诉风险。因为数面投诉渠道本来就有很多很多个，然后他每个人说话的方式都不一样，所以我们就要把这个字法给他。所以就要用发散性的思维打投诉，把那个接线的或者是客户表现这种啊话语，用那个模型的特定的方式来进行总结。这个就是包含中信部投诉媒体，然后幺二三幺五总部投诉人大还有什么检举，闹光法院重上起诉检举，还有还有法院的国务院这种等等这种所有的所有的深边投资渠道，他可能都要喊报到。然后他可能会说，我要向三幺五投诉，我要我要通过三幺五投诉。就是你每个说话的方式，你可能都要考虑进去，就用这种发散性的思维是一个模型的关键字。每每种种加方方式都写进。对，尽量全一点。然后你要排除还要排除一些你可能说到的，但是又不符合你这个场景的，就要用花费心思的去把这个东西进行优化。因为我们每一个模型，它这个上限进行搭建好之后，都是有模型的命中准确率。然后覆盖率你不是说你覆盖的越多越好，其实是你要的那一部分的场景更加符合贴近你的诉求的才更好。所以这个就是模型会有一个相关的规则，嗯，是看一下来摆培训的材料。虽然这个培训材料很久了，但是这个因为一直没有改，它是会有一个相关手册的，就是有嗯过非与就是铁的关系。嗯，过呢就是只要其中一种嗯，非呢就是排除就这种文本，可以到时候也研究一下，因为不一定为得到啊，因为我们这些都是因为有公需嘛才才可能用到的。你只是因为有这个便a的一个这种手段，就是能够更好的筛选好数据啊，符合你的一个素材的。还有申报的要求，是客户申报，还是说坐型申报，还有绝对位置以及呃那个分贝，就是那个声音的音量的高低。如果是说太大声了，怎么样的这种都有都有一些设定的关键词的。还有静音。就是你整个电话里面如果你长时间没有说话，静音超多长秒之前呢，都是不是有都有这种关于服务质量的，认准的嘛。所以如果你长时间没有说话，你又没有任何的一些呃嗯啊那种不是做起来的，都都会有一些像那种磨损。嗯，这个就是一个语音的模型。一般呢我们就我们就会根据自己的需要来进行建。但是因为这个模型的承载量其实它是有限的。所以我们一般建模型的话啊，就是尽量能够别人不能见了，我们可能就会用的他的一起来那个有可能在他的这个在优化也可能但是我们基本上就是我们自己不会去动别人的东西，只能自己只用自己搭建的这个就是一个模型配置。然后上线了之后呢，就会可以在里边下载相关的数据，然后设置一些最近要跑一下，还有考研计划。这种呢设置好之后，它也可以由系统直接给你跑出来你预设的这个数据，然后也可以自己通过这个线上来进行集核。但是需要输入相对应的模型的那个模板。比方说像他们这种模型设的准确啊，申报是否什么什么转型错误呀，还有我们自己设置的我们自器设置的这个的历史不可的模式。这个就是我们自己搭建。我们自己搭建了之后呢，我们还会有一个就是相对考这个机器人的，我们也我们也可以给看一下，像这个模型我们就已经设置好了，预先设置好了一些考对象，我们就可以直接点击放音。然后这个就可以听到对应的一个录音一个详情。然后如果需要进行质检，我们就在里面选择质检模吧。然后按照预先设定的来进行开始，这个就是我们前期设置好的排版。嗯，有质检评语，就是整个对这个的一个评价。我们因为是涉及到投诉工单的，我们会有对应的工单流程号，然后涉及到的工单，然后是这个工单是是下怕了呢，还是自己装去直接归档了，是不是合格？好，之前都会有一个，因为现在目前都有机器人在做。把它做完了之后，我们再通过机器人的数据来完成人工复审。这个就是一个语音的是谢。我再给你们看一下烘单的这件烘单衬这件工能质检。智能质检呢就在这个统一工能智能质检也是一个高能模型配置。这个呢看就就比较简单一些，我们这个也是有一种几句材料也比较久，但是个能你没有多大的感应，我还是能够用得上的。像我们现在目前自己不新的很多的一个模型。然后呢，像他们现在用的比较多的可能用于做那个前台的提单降量，它会有一些会有一些叙数的，就以质检一个情况。就对于这种模型就不会像刚刚那样那么多的关键字。因为这个就是一个工单内容里面的一个关键字的提取，所以就不会有表述的那么多。我们就因为这个提单一般都是客不代表自己填的，他们相对就比客户说的那种话稍微专业那么一点点，所以他那个就没那么多的关键字。他设置好了关键字之后，设置好关键字之后，我们就可以把这个模型根据自己的相相对应的条件来设置模型名称关联的环节，关联的环节。这意任意一个环节都可以像创建工单一样，创建工单就是那种投诉提单的社会内容。然后工单反馈归纳就是处理结构里面的一个内容。然后看一下模型的一些新片的规则，文本关键字都ok.那关键字就是根据考试内容或者是处理结果，你要命中的那些关键字，或者需要孩子的关键字来进行。如果是说影响这个影响这个关键词，比如现在关键词出现了几次，你就可以对应的选择几次都是会有一些属性的。复说完个点儿，但这个相对应的没有语音的那么复杂。然后说说好模型条件，这个还有一个反选，就是我已经删出了一部分数据，我要看这一部分数据里面有没有我说的那些关键字。那我就可以在进行模型预览的过程当中，把我要的那部分数据上传到里面，然后再用我的预设的关键字把这个跑出来，然后它就会符合我这个模型里面的预设。好的关键字的这部分数据它就会出来，同时还可以导出这个它也是有也是有也是有需要设置考评的一些流程的。我们像搭建模型之后，这只是一个搭建的模型。我们同时需要它跑出数据的话，我们还需要设置考评模板，我就自动自动考评之后，这里有一个自动考评和人工考评自动考评。就是说我通过这个预售的关键字，还有系统直接帮我跑出来。然后人工考评就是我关联了环节之后，我再进行通过工单，选择好我预售这些关键字。在这个工单综合查询里面，选择我的质检模板，然后选择人工考评项。然后我对这个工单完成线上的机会。那个小组长，这个就是我们之前设置好的这个好对象。这个就是真的就是我们预先设置了一个下化功能的。这个呢就是下派这个有绿色标识的，就是我们刚联大的这一个环节，它就会打一个绿色的。这个其他情况下都是灰色的。我们就看他这个当时写的三个意见是什么。因为我们合的就是下帕的那一个环节，它是不是按钮中走的，他备注的是床头诉。那我们就需要看他的三个原因是什么？业务名称、通知焦点还有是不是合格，然后给他进行一个评语。你如果早早先控于这个被质检，人因就可以本字通知，并性也可以点击宝贝后，他会提交这一个工单的质检就完成了。这个就是一个线上的这格，也就是关单智能质检。因为这些东西都是没有没有专门的人给我们培训的。就是本部给了一个培训材料，可能线上讲了一下完，然后我们其他的都有，我们自己去摸合。再根据加的那个本部老师的一些微信啊，或者是你那个一个办公在进行跟他们了解这个需求，或者是了解我们这个可不可以实现，就是慢慢的摸索出来的一些东西。你这一点有什么问题吗？就无所谓。就是我们不是说下午要去上面跟一下班，嗯，他们他们他们在操作过程中会有用到，这些些是他们不会，我们才会用这个工单，智能质检和语音智能质检上面的投诉处理员基本上不会用到。但是我们会用这些模型，这些这种手段来进行结合他们处理的工单处理的那个回电话的录音，这个是会有的。就是机器人处理完之后，到这个不是机器人处理，这个就是系统直接预算好的关键字跑出来的模型数据。因我们机器人有可能就是用模型数据跑出来的数据，然后再进行完工复审。但是这只是部分机器人是这样子。很多的那个那个机器人是通过本身已有的报表啊或者是什么的来进行实现的。那个IPA不是一定通过这个智能化手段相结合的。有可能是因为报表结合报表加机器人，或者是说模型加机器人，或者模型加这个报表，再加机器人，还有可能加自然体，就多个手段运用都有可能。只是说有些我们没有做的那么复杂，这就是我们现在目前一样等一些。然后我们建的模型其实也不是特别的低于，只会根据我们自己当下的一些生产的需要啊，或者是认为自己的需要重新搭，或者是在人们的基础上重新优化。就这样子的有些模型可能用啊，都已经很久很久了。你看像二零二零年的就是本部搭建的模型，然后我们自己搭建的模型，可能最早的最早的也就是二一年拿的，二一年拿到二零年才这种模型。如果是一直都有有着的那就说明他这个模型一直都有用。我们我们会不断去，你不定期的进行看一下，是不是没有用了，就不是就说说要优化，要下线了，我们就会把这个模型下线再进行优化。还有问题吗？这是湖南政治点，我就是这件。然后这些东西都是都是比较会考意的，也就是说要要用真的是要用发散，一心去去这这个模型搭建，想好想好的一些关键字，然后再去搭模型关联环节。这次间其实就是关键字，尤其这样的一个东西，你的关键字就要阐述就是比较多。你再看一下别人别人的那个，他们这样不同。嗯，好的，为了能活在一起呢，不是有一些整个课件分享嘛，分享一下那个应用的。那么这个就是我工单模型在于我们形象的时候，非常知道，我们就会根据国家分那东西，他就是没有收到短信提醒一个信息。那像他这个呢就会关理到一些，注意警全。然后他说在这处理语言是直接处理还是退费的时候，指定一个工单归档。工单归档的内容呢，我们一般会有工单反馈归档和工单归档两个环还是女生，对不对？或者是你什么时间给这种环境是需要想象。那就是然后可能什么类型这个感叹号嘛，感叹号就是代表的。然后这一个数据就是代表的货的关系，就是有下发记录或者有查证记录货的。加这个呢这些牌子这些已经发了的。然后最后是投诉处理人，直接处理后后再那呢会备一案申请。这个就是要包含三个，就是我尽量有进行层些巨渊问一下大家好。然后还有这种，我们每个省份家是不一样的。所以这个他会有一个对应的，不知道还是比较多的。他们前景应该是都比较顺，都已经讲好了，然后才会进行分享，而且应该是会比较成熟的。你你个你你个屁，你们也可以播一下，就这样就跟你们说。嗯，还有一个点，这个点我觉得可以说一下，就是我们这个系统运行外汇，就是对于一部分有关吧。对于一部分的工单，就是通过中国移动APP上面这个受理渠道提交的工单，由我们预测外呼，也就是系统直接去给客户打电话，联系他的那个预留的联系电话号码打三次。如果是说呃就是它会由它会由系统直接打通。打通了之后呢，它就会转人工，就是日前进行拨一下，拨一下，拨完了，你打了三次，如果还没有接通，你就把这个放到那个人工那里去人工再跟一次就可以了。但是如果我之前第一次打，我就已经打通了，我就直接把这个电话转到人工那里，这个就节省了那个节省了那个呼的那个次数。同时也提提升了这个接通率，就实现在很新东西一直在做的。然后我觉得还是可以实现一些节省通发市场的样啊，但是相应的相业这个需求，我做了一些相关的，还有一些自动化的数据的一些优化需求。还有建立单这些都都已经做了的话，没有比较新的智能体真人体还现在在做，还在测试。像我像我自己现在负责那个智能体，是不满意素颜的赠品。嗯，现在是隔段时间就会要复审，一批数据还没有上线，都是上个月是二号这个成分我这上也是已经上线了的，但是我不是个不满意的。因为这东西优化优化，然后你要达到你的预期设定的一些啊的大案上要比较。对，不然等一下上线了都出问题。你尝试了一下线，我们十六月份在这搞的这个快递，现在还没有上线，市场那边在开。嗯，楼上那个四楼的，我也不知道他们是哪个部门的，反正你说那个自然局，他们每个人都记得有几个吧。然后各个部门都会有提交证据啊，智能什么智能数字化的这种东西需求，所以他们应该也还比较忙。我这个现在是一直在库存，一直在不用看的，只是说还没有还没有上信，还没有通过你人吗？没有没有什么问题就休息一下。可以了，这个就楼下楼点个外卖吧，你就是这个人肉，今天要下雨今天快下雨了。